{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "integer"], "source": ["atom", "id"], "primary_key": true, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "title"], "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": false}, "name": ["atom", "title"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "body"], "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "body"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": false, "type": ["atom", "integer"], "source": ["atom", "published"], "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "published"], "type": ["atom", "boolean"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 0, "type": ["atom", "integer"], "source": ["atom", "view_count"], "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "view_count"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "integer"], "source": ["atom", "user_id"], "primary_key": false, "foreign_key": true, "check_constraints": [], "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "inserted_at"], "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": false}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "updated_at"], "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": false}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "source": ["atom", "posts"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "integer"], "source": ["atom", "id"], "primary_key": true, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [{"attributes": {"field": ["atom", "user_id"], "association_name": ["atom", "user"], "references_field": ["atom", "id"], "references_table": ["atom", "users"]}, "__struct__": "ForeignKey"}], "indices": {"attributes": {"indices": [{"attributes": {"name": ["atom", "posts_title_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "title"], "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": false}, "name": ["atom", "title"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "posts_published_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": false, "type": ["atom", "integer"], "source": ["atom", "published"], "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "published"], "type": ["atom", "boolean"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "posts_user_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "integer"], "source": ["atom", "user_id"], "primary_key": false, "foreign_key": true, "check_constraints": [], "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}]}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}